import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Environment, Grid } from '@react-three/drei';
import * as THREE from 'three';
import { ThreeControls } from '../three-controls/three-controls';
import './three-renderer.css';

// 基础几何体组件
interface GeometryProps {
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: [number, number, number];
  color?: string;
  metalness?: number;
  roughness?: number;
  wireframe?: boolean;
}

// 立方体组件
const Cube: React.FC<GeometryProps> = ({
  position = [0, 0, 0],
  rotation = [0, 0, 0],
  scale = [1, 1, 1],
  color = '#2269EC',
  metalness = 0.5,
  roughness = 0.5,
  wireframe = false
}) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.2;
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3;
    }
  });

  return (
    <mesh ref={meshRef} position={position} rotation={rotation} scale={scale}>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial
        color={color}
        metalness={metalness}
        roughness={roughness}
        wireframe={wireframe}
      />
    </mesh>
  );
};

// 球体组件
const Sphere: React.FC<GeometryProps> = ({
  position = [2, 0, 0],
  rotation = [0, 0, 0],
  scale = [1, 1, 1],
  color = '#EC2269',
  metalness = 0.8,
  roughness = 0.2,
  wireframe = false
}) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.5;
    }
  });

  return (
    <mesh ref={meshRef} position={position} rotation={rotation} scale={scale}>
      <sphereGeometry args={[0.5, 32, 32]} />
      <meshStandardMaterial
        color={color}
        metalness={metalness}
        roughness={roughness}
        wireframe={wireframe}
      />
    </mesh>
  );
};

// 圆环组件
const Torus: React.FC<GeometryProps> = ({
  position = [-2, 0, 0],
  rotation = [0, 0, 0],
  scale = [1, 1, 1],
  color = '#69EC22',
  metalness = 0.3,
  roughness = 0.7,
  wireframe = false
}) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.5;
      meshRef.current.rotation.z = state.clock.elapsedTime * 0.3;
    }
  });

  return (
    <mesh ref={meshRef} position={position} rotation={rotation} scale={scale}>
      <torusGeometry args={[0.5, 0.2, 16, 100]} />
      <meshStandardMaterial
        color={color}
        metalness={metalness}
        roughness={roughness}
        wireframe={wireframe}
      />
    </mesh>
  );
};

// 场景组件
const Scene3D: React.FC<{
  materialSettings?: {
    color: string;
    metalness: number;
    roughness: number;
    glass: number;
  };
  showGrid?: boolean;
  wireframe?: boolean;
  lightingEnabled?: boolean;
}> = ({ materialSettings, showGrid = true, wireframe = false, lightingEnabled = true }) => {
  return (
    <>
      {/* 光照系统 */}
      {lightingEnabled && (
        <>
          <ambientLight intensity={0.4} />
          <directionalLight
            position={[10, 10, 5]}
            intensity={1}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          <pointLight position={[-10, -10, -10]} intensity={0.5} color="#ff6b6b" />
        </>
      )}
      
      {/* 基础几何体 */}
      <group>
        <Cube
          color={materialSettings?.color || '#2269EC'}
          metalness={materialSettings?.metalness ? materialSettings.metalness / 100 : 0.5}
          roughness={materialSettings?.roughness ? materialSettings.roughness / 100 : 0.5}
          wireframe={wireframe}
        />
        <Sphere
          metalness={materialSettings?.metalness ? materialSettings.metalness / 100 : 0.8}
          roughness={materialSettings?.roughness ? materialSettings.roughness / 100 : 0.2}
          wireframe={wireframe}
        />
        <Torus
          metalness={materialSettings?.metalness ? materialSettings.metalness / 100 : 0.3}
          roughness={materialSettings?.roughness ? materialSettings.roughness / 100 : 0.7}
          wireframe={wireframe}
        />
      </group>
      
      {/* 地面网格 */}
      {showGrid && (
        <Grid
          position={[0, -1, 0]}
          args={[10, 10]}
          cellSize={0.5}
          cellThickness={0.5}
          cellColor="#6f6f6f"
          sectionSize={2}
          sectionThickness={1}
          sectionColor="#9d4b4b"
          fadeDistance={25}
          fadeStrength={1}
          followCamera={false}
          infiniteGrid={true}
        />
      )}
    </>
  );
};

// 主渲染器组件
interface ThreeRendererProps {
  className?: string;
  materialSettings?: {
    color: string;
    metalness: number;
    roughness: number;
    glass: number;
  };
}

export const ThreeRenderer: React.FC<ThreeRendererProps> = ({
  className = '',
  materialSettings
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [wireframe, setWireframe] = useState(false);
  const [lightingEnabled, setLightingEnabled] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const controlsRef = useRef<any>(null);

  useEffect(() => {
    // 模拟加载时间
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // 控制函数
  const handleResetView = useCallback(() => {
    if (controlsRef.current) {
      controlsRef.current.reset();
    }
  }, []);

  const handleZoomIn = useCallback(() => {
    if (controlsRef.current) {
      controlsRef.current.dollyIn(0.8);
      controlsRef.current.update();
    }
  }, []);

  const handleZoomOut = useCallback(() => {
    if (controlsRef.current) {
      controlsRef.current.dollyOut(0.8);
      controlsRef.current.update();
    }
  }, []);

  const handleToggleGrid = useCallback(() => {
    setShowGrid(!showGrid);
  }, [showGrid]);

  const handleToggleWireframe = useCallback(() => {
    setWireframe(!wireframe);
  }, [wireframe]);

  const handleToggleLighting = useCallback(() => {
    setLightingEnabled(!lightingEnabled);
  }, [lightingEnabled]);

  const handleToggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  const handleCameraPreset = useCallback((preset: string) => {
    if (controlsRef.current) {
      const camera = controlsRef.current.object;
      const target = controlsRef.current.target;

      switch (preset) {
        case 'front':
          camera.position.set(0, 0, 5);
          break;
        case 'back':
          camera.position.set(0, 0, -5);
          break;
        case 'left':
          camera.position.set(-5, 0, 0);
          break;
        case 'right':
          camera.position.set(5, 0, 0);
          break;
        case 'top':
          camera.position.set(0, 5, 0);
          break;
        case 'bottom':
          camera.position.set(0, -5, 0);
          break;
        case 'isometric':
          camera.position.set(5, 5, 5);
          break;
        default:
          camera.position.set(5, 5, 5);
      }

      target.set(0, 0, 0);
      controlsRef.current.update();
    }
  }, []);

  return (
    <div className={`three-renderer ${className} ${isFullscreen ? 'fullscreen' : ''}`}>
      {isLoading && (
        <div className="three-renderer__loading">
          <div className="loading-spinner"></div>
          <span>加载3D场景中...</span>
        </div>
      )}

      <Canvas
        className="three-renderer__canvas"
        shadows
        camera={{ position: [5, 5, 5], fov: 60 }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        {/* 透视相机 */}
        <PerspectiveCamera makeDefault position={[5, 5, 5]} />

        {/* 轨道控制器 */}
        <OrbitControls
          ref={controlsRef}
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={2}
          maxDistance={20}
          maxPolarAngle={Math.PI / 2}
        />

        {/* 环境贴图 */}
        <Environment preset="city" />

        {/* 3D场景 */}
        <Scene3D
          materialSettings={materialSettings}
          showGrid={showGrid}
          wireframe={wireframe}
          lightingEnabled={lightingEnabled}
        />
      </Canvas>

      {/* 3D控制面板 */}
      <ThreeControls
        onResetView={handleResetView}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onToggleGrid={handleToggleGrid}
        onToggleWireframe={handleToggleWireframe}
        onToggleLighting={handleToggleLighting}
        onToggleFullscreen={handleToggleFullscreen}
        onCameraPreset={handleCameraPreset}
      />
      
      {/* 渲染信息 */}
      <div className="three-renderer__info">
        <div className="info-item">
          <span className="info-label">FPS</span>
          <span className="info-value">60</span>
        </div>
        <div className="info-item">
          <span className="info-label">三角面</span>
          <span className="info-value">2.4K</span>
        </div>
        <div className="info-item">
          <span className="info-label">顶点</span>
          <span className="info-value">1.2K</span>
        </div>
      </div>
    </div>
  );
};

export default ThreeRenderer;
