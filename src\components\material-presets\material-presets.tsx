import React, { useState } from 'react';
import './material-presets.css';

interface MaterialPreset {
  id: string;
  name: string;
  color: string;
  metalness: number;
  roughness: number;
  glass: number;
  preview: string;
}

interface MaterialPresetsProps {
  onPresetSelect?: (preset: MaterialPreset) => void;
  currentSettings?: {
    color: string;
    metalness: number;
    roughness: number;
    glass: number;
  };
  className?: string;
}

const materialPresets: MaterialPreset[] = [
  {
    id: 'chrome',
    name: '铬金属',
    color: '#C0C0C0',
    metalness: 100,
    roughness: 5,
    glass: 0,
    preview: 'linear-gradient(135deg, #C0C0C0, #E8E8E8)'
  },
  {
    id: 'gold',
    name: '黄金',
    color: '#FFD700',
    metalness: 100,
    roughness: 10,
    glass: 0,
    preview: 'linear-gradient(135deg, #FFD700, #FFA500)'
  },
  {
    id: 'copper',
    name: '铜质',
    color: '#B87333',
    metalness: 90,
    roughness: 20,
    glass: 0,
    preview: 'linear-gradient(135deg, #B87333, #CD7F32)'
  },
  {
    id: 'plastic',
    name: '塑料',
    color: '#2269EC',
    metalness: 0,
    roughness: 80,
    glass: 0,
    preview: 'linear-gradient(135deg, #2269EC, #1557D3)'
  },
  {
    id: 'rubber',
    name: '橡胶',
    color: '#2C2C2C',
    metalness: 0,
    roughness: 95,
    glass: 0,
    preview: 'linear-gradient(135deg, #2C2C2C, #1A1A1A)'
  },
  {
    id: 'glass',
    name: '玻璃',
    color: '#87CEEB',
    metalness: 0,
    roughness: 0,
    glass: 90,
    preview: 'linear-gradient(135deg, rgba(135, 206, 235, 0.3), rgba(135, 206, 235, 0.6))'
  },
  {
    id: 'ceramic',
    name: '陶瓷',
    color: '#F5F5DC',
    metalness: 0,
    roughness: 15,
    glass: 0,
    preview: 'linear-gradient(135deg, #F5F5DC, #E6E6FA)'
  },
  {
    id: 'wood',
    name: '木质',
    color: '#8B4513',
    metalness: 0,
    roughness: 85,
    glass: 0,
    preview: 'linear-gradient(135deg, #8B4513, #A0522D)'
  },
  {
    id: 'fabric',
    name: '布料',
    color: '#696969',
    metalness: 0,
    roughness: 100,
    glass: 0,
    preview: 'linear-gradient(135deg, #696969, #808080)'
  },
  {
    id: 'carbon',
    name: '碳纤维',
    color: '#1C1C1C',
    metalness: 20,
    roughness: 30,
    glass: 0,
    preview: 'linear-gradient(135deg, #1C1C1C, #2F2F2F)'
  },
  {
    id: 'aluminum',
    name: '铝合金',
    color: '#A8A8A8',
    metalness: 80,
    roughness: 25,
    glass: 0,
    preview: 'linear-gradient(135deg, #A8A8A8, #C0C0C0)'
  },
  {
    id: 'titanium',
    name: '钛合金',
    color: '#878681',
    metalness: 85,
    roughness: 15,
    glass: 0,
    preview: 'linear-gradient(135deg, #878681, #A0A0A0)'
  }
];

export const MaterialPresets: React.FC<MaterialPresetsProps> = ({
  onPresetSelect,
  currentSettings,
  className = ''
}) => {
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);

  const handlePresetClick = (preset: MaterialPreset) => {
    setSelectedPreset(preset.id);
    onPresetSelect?.(preset);
  };

  const isCurrentPreset = (preset: MaterialPreset) => {
    if (!currentSettings) return false;
    
    return (
      currentSettings.color === preset.color &&
      currentSettings.metalness === preset.metalness &&
      currentSettings.roughness === preset.roughness &&
      currentSettings.glass === preset.glass
    );
  };

  return (
    <div className={`material-presets ${className}`}>
      <div className="presets-header">
        <h3>材质预设</h3>
        <span className="presets-count">{materialPresets.length} 种材质</span>
      </div>
      
      <div className="presets-grid">
        {materialPresets.map((preset) => (
          <div
            key={preset.id}
            className={`preset-item ${
              isCurrentPreset(preset) || selectedPreset === preset.id ? 'active' : ''
            }`}
            onClick={() => handlePresetClick(preset)}
            title={preset.name}
          >
            <div 
              className="preset-preview"
              style={{ background: preset.preview }}
            >
              <div className="preset-shine"></div>
            </div>
            <div className="preset-info">
              <span className="preset-name">{preset.name}</span>
              <div className="preset-properties">
                <span className="property">M:{preset.metalness}</span>
                <span className="property">R:{preset.roughness}</span>
                {preset.glass > 0 && <span className="property">G:{preset.glass}</span>}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MaterialPresets;
