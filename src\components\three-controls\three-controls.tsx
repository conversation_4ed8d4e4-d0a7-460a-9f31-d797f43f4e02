import React, { useState } from 'react';
import { IconButton } from '../icon-button/icon-button';
import { SceneSettings } from '../scene-settings/scene-settings';
import { 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Move3D, 
  Eye, 
  EyeOff,
  Grid3X3,
  Sun,
  Moon,
  Maximize,
  Minimize,
  Camera,
  Settings
} from 'lucide-react';
import './three-controls.css';

interface ThreeControlsProps {
  onResetView?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onToggleGrid?: () => void;
  onToggleWireframe?: () => void;
  onToggleLighting?: () => void;
  onToggleFullscreen?: () => void;
  onCameraPreset?: (preset: string) => void;
  onToggleSettings?: () => void;
  className?: string;
}

export const ThreeControls: React.FC<ThreeControlsProps> = ({
  onResetView,
  onZoomIn,
  onZoomOut,
  onToggleGrid,
  onToggleWireframe,
  onToggleLighting,
  onToggleFullscreen,
  onCameraPreset,
  onToggleSettings,
  className = ''
}) => {
  const [isGridVisible, setIsGridVisible] = useState(true);
  const [isWireframe, setIsWireframe] = useState(false);
  const [isLightingEnabled, setIsLightingEnabled] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showCameraMenu, setShowCameraMenu] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const handleToggleGrid = () => {
    setIsGridVisible(!isGridVisible);
    onToggleGrid?.();
  };

  const handleToggleWireframe = () => {
    setIsWireframe(!isWireframe);
    onToggleWireframe?.();
  };

  const handleToggleLighting = () => {
    setIsLightingEnabled(!isLightingEnabled);
    onToggleLighting?.();
  };

  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    onToggleFullscreen?.();
  };

  const handleCameraPreset = (preset: string) => {
    onCameraPreset?.(preset);
    setShowCameraMenu(false);
  };

  const cameraPresets = [
    { name: '前视图', value: 'front' },
    { name: '后视图', value: 'back' },
    { name: '左视图', value: 'left' },
    { name: '右视图', value: 'right' },
    { name: '顶视图', value: 'top' },
    { name: '底视图', value: 'bottom' },
    { name: '等轴视图', value: 'isometric' }
  ];

  return (
    <div className={`three-controls ${className}`}>
      {/* 主要控制按钮 */}
      <div className="controls-group">
        <IconButton
          icon={RotateCcw}
          onClick={onResetView}
          size="small"
          title="重置视图"
        />
        <IconButton
          icon={ZoomIn}
          onClick={onZoomIn}
          size="small"
          title="放大"
        />
        <IconButton
          icon={ZoomOut}
          onClick={onZoomOut}
          size="small"
          title="缩小"
        />
      </div>

      {/* 显示控制 */}
      <div className="controls-group">
        <IconButton
          icon={Grid3X3}
          onClick={handleToggleGrid}
          size="small"
          title={isGridVisible ? "隐藏网格" : "显示网格"}
          className={isGridVisible ? 'active' : ''}
        />
        <IconButton
          icon={isWireframe ? Eye : EyeOff}
          onClick={handleToggleWireframe}
          size="small"
          title={isWireframe ? "关闭线框" : "开启线框"}
          className={isWireframe ? 'active' : ''}
        />
        <IconButton
          icon={isLightingEnabled ? Sun : Moon}
          onClick={handleToggleLighting}
          size="small"
          title={isLightingEnabled ? "关闭光照" : "开启光照"}
          className={isLightingEnabled ? 'active' : ''}
        />
      </div>

      {/* 相机预设 */}
      <div className="controls-group camera-group">
        <IconButton
          icon={Camera}
          onClick={() => setShowCameraMenu(!showCameraMenu)}
          size="small"
          title="相机预设"
          className={showCameraMenu ? 'active' : ''}
        />
        {showCameraMenu && (
          <div className="camera-menu">
            {cameraPresets.map((preset) => (
              <button
                key={preset.value}
                className="camera-preset-button"
                onClick={() => handleCameraPreset(preset.value)}
              >
                {preset.name}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* 其他控制 */}
      <div className="controls-group">
        <IconButton
          icon={isFullscreen ? Minimize : Maximize}
          onClick={handleToggleFullscreen}
          size="small"
          title={isFullscreen ? "退出全屏" : "全屏显示"}
        />
        <IconButton
          icon={Settings}
          onClick={() => setShowSettings(true)}
          size="small"
          title="渲染设置"
        />
      </div>

      {/* 场景设置面板 */}
      <SceneSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onSettingsChange={(settings) => {
          console.log('场景设置更新:', settings);
          // 这里可以添加场景设置更新逻辑
        }}
      />
    </div>
  );
};

export default ThreeControls;
