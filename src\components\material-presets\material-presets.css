/* 材质预设组件样式 */
.material-presets {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.presets-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
}

.presets-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-content-accent);
}

.presets-count {
  font-size: 12px;
  color: var(--color-content-mute);
}

.presets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
}

.preset-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px;
  background: var(--color-input-background);
  border-radius: var(--radius-m);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.preset-item:hover {
  background: var(--color-support);
  border-color: var(--color-border);
  transform: translateY(-1px);
}

.preset-item.active {
  background: var(--color-support);
  border-color: var(--color-brand);
  box-shadow: 0 0 0 1px var(--color-brand);
}

.preset-preview {
  width: 100%;
  height: 40px;
  border-radius: var(--radius-s);
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.preset-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s ease;
}

.preset-item:hover .preset-shine {
  left: 100%;
}

.preset-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: center;
}

.preset-name {
  font-size: 11px;
  font-weight: 500;
  color: var(--color-content-regular);
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.preset-properties {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
}

.property {
  font-size: 9px;
  color: var(--color-content-mute);
  background: rgba(0, 0, 0, 0.2);
  padding: 1px 4px;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
}

/* 滚动条样式 */
.presets-grid::-webkit-scrollbar {
  width: 4px;
}

.presets-grid::-webkit-scrollbar-track {
  background: var(--color-input-background);
  border-radius: 2px;
}

.presets-grid::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 2px;
}

.presets-grid::-webkit-scrollbar-thumb:hover {
  background: var(--color-content-mute);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .presets-grid {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: 6px;
  }
  
  .preset-item {
    padding: 6px;
    gap: 4px;
  }
  
  .preset-preview {
    height: 35px;
  }
  
  .preset-name {
    font-size: 10px;
  }
  
  .property {
    font-size: 8px;
    padding: 1px 3px;
  }
}

/* 动画效果 */
.material-presets {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.preset-item {
  animation: scaleIn 0.2s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 特殊材质效果 */
.preset-item[data-preset="chrome"] .preset-preview {
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 10px rgba(192, 192, 192, 0.5);
}

.preset-item[data-preset="gold"] .preset-preview {
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 8px rgba(255, 215, 0, 0.4);
}

.preset-item[data-preset="glass"] .preset-preview {
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 12px rgba(135, 206, 235, 0.3);
  backdrop-filter: blur(2px);
}

/* 深色主题适配 */
.theme-dark .preset-item {
  background: var(--color-input-background);
}

.theme-dark .preset-item:hover {
  background: var(--color-support);
}

.theme-dark .property {
  background: rgba(0, 0, 0, 0.4);
}

/* 浅色主题适配 */
.theme-light .preset-item {
  background: rgba(0, 0, 0, 0.05);
}

.theme-light .preset-item:hover {
  background: rgba(0, 0, 0, 0.1);
}

.theme-light .property {
  background: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.6);
}

.theme-light .preset-name {
  color: rgba(0, 0, 0, 0.8);
}

.theme-light .presets-count {
  color: rgba(0, 0, 0, 0.5);
}

/* 加载状态 */
.preset-item.loading {
  opacity: 0.6;
  pointer-events: none;
}

.preset-item.loading .preset-preview {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
