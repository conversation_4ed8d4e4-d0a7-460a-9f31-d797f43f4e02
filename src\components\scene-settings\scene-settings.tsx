import React, { useState } from 'react';
import { Slider } from '../slider/slider';
import { IconButton } from '../icon-button/icon-button';
import { 
  X, 
  RotateCw, 
  Zap, 
  Eye, 
  Palette, 
  Settings,
  Sun,
  Moon,
  Lightbulb,
  Contrast
} from 'lucide-react';
import './scene-settings.css';

interface SceneSettingsProps {
  isOpen?: boolean;
  onClose?: () => void;
  onSettingsChange?: (settings: SceneSettings) => void;
  className?: string;
}

export interface SceneSettings {
  // 光照设置
  ambientIntensity: number;
  directionalIntensity: number;
  pointLightIntensity: number;
  
  // 环境设置
  environmentPreset: string;
  backgroundType: 'color' | 'environment' | 'transparent';
  backgroundColor: string;
  
  // 渲染设置
  shadows: boolean;
  antialias: boolean;
  toneMapping: string;
  exposure: number;
  
  // 后处理效果
  bloom: boolean;
  bloomIntensity: number;
  contrast: number;
  saturation: number;
  
  // 动画设置
  autoRotate: boolean;
  rotationSpeed: number;
  animationEnabled: boolean;
}

const defaultSettings: SceneSettings = {
  ambientIntensity: 0.4,
  directionalIntensity: 1.0,
  pointLightIntensity: 0.5,
  environmentPreset: 'city',
  backgroundType: 'environment',
  backgroundColor: '#1a1a1a',
  shadows: true,
  antialias: true,
  toneMapping: 'ACESFilmic',
  exposure: 1.0,
  bloom: false,
  bloomIntensity: 0.5,
  contrast: 1.0,
  saturation: 1.0,
  autoRotate: false,
  rotationSpeed: 1.0,
  animationEnabled: true
};

export const SceneSettings: React.FC<SceneSettingsProps> = ({
  isOpen = false,
  onClose,
  onSettingsChange,
  className = ''
}) => {
  const [settings, setSettings] = useState<SceneSettings>(defaultSettings);
  const [activeTab, setActiveTab] = useState<'lighting' | 'environment' | 'rendering' | 'effects'>('lighting');

  const handleSettingChange = (key: keyof SceneSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    onSettingsChange?.(newSettings);
  };

  const resetToDefaults = () => {
    setSettings(defaultSettings);
    onSettingsChange?.(defaultSettings);
  };

  if (!isOpen) return null;

  const environmentPresets = [
    { value: 'city', label: '城市' },
    { value: 'sunset', label: '日落' },
    { value: 'dawn', label: '黎明' },
    { value: 'night', label: '夜晚' },
    { value: 'studio', label: '工作室' },
    { value: 'warehouse', label: '仓库' }
  ];

  const toneMappingOptions = [
    { value: 'None', label: '无' },
    { value: 'Linear', label: '线性' },
    { value: 'Reinhard', label: 'Reinhard' },
    { value: 'Cineon', label: 'Cineon' },
    { value: 'ACESFilmic', label: 'ACES电影' }
  ];

  return (
    <div className={`scene-settings ${className}`}>
      <div className="scene-settings__overlay" onClick={onClose} />
      
      <div className="scene-settings__panel">
        <div className="settings-header">
          <h3>场景设置</h3>
          <div className="header-actions">
            <IconButton
              icon={RotateCw}
              onClick={resetToDefaults}
              size="small"
              title="重置为默认值"
            />
            <IconButton
              icon={X}
              onClick={onClose}
              size="small"
              title="关闭"
            />
          </div>
        </div>

        <div className="settings-tabs">
          <button
            className={`tab-button ${activeTab === 'lighting' ? 'active' : ''}`}
            onClick={() => setActiveTab('lighting')}
          >
            <Sun size={16} />
            光照
          </button>
          <button
            className={`tab-button ${activeTab === 'environment' ? 'active' : ''}`}
            onClick={() => setActiveTab('environment')}
          >
            <Palette size={16} />
            环境
          </button>
          <button
            className={`tab-button ${activeTab === 'rendering' ? 'active' : ''}`}
            onClick={() => setActiveTab('rendering')}
          >
            <Settings size={16} />
            渲染
          </button>
          <button
            className={`tab-button ${activeTab === 'effects' ? 'active' : ''}`}
            onClick={() => setActiveTab('effects')}
          >
            <Zap size={16} />
            效果
          </button>
        </div>

        <div className="settings-content">
          {activeTab === 'lighting' && (
            <div className="settings-section">
              <div className="setting-group">
                <label className="setting-label">
                  <Lightbulb size={14} />
                  环境光强度
                </label>
                <Slider
                  value={settings.ambientIntensity}
                  min={0}
                  max={2}
                  step={0.1}
                  onChange={(value) => handleSettingChange('ambientIntensity', value)}
                  showValue
                />
              </div>

              <div className="setting-group">
                <label className="setting-label">
                  <Sun size={14} />
                  方向光强度
                </label>
                <Slider
                  value={settings.directionalIntensity}
                  min={0}
                  max={3}
                  step={0.1}
                  onChange={(value) => handleSettingChange('directionalIntensity', value)}
                  showValue
                />
              </div>

              <div className="setting-group">
                <label className="setting-label">
                  <Zap size={14} />
                  点光源强度
                </label>
                <Slider
                  value={settings.pointLightIntensity}
                  min={0}
                  max={2}
                  step={0.1}
                  onChange={(value) => handleSettingChange('pointLightIntensity', value)}
                  showValue
                />
              </div>

              <div className="setting-group">
                <label className="setting-label">
                  <Eye size={14} />
                  阴影
                </label>
                <div className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={settings.shadows}
                    onChange={(e) => handleSettingChange('shadows', e.target.checked)}
                  />
                  <span className="toggle-slider"></span>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'environment' && (
            <div className="settings-section">
              <div className="setting-group">
                <label className="setting-label">环境预设</label>
                <select
                  value={settings.environmentPreset}
                  onChange={(e) => handleSettingChange('environmentPreset', e.target.value)}
                  className="setting-select"
                >
                  {environmentPresets.map(preset => (
                    <option key={preset.value} value={preset.value}>
                      {preset.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="setting-group">
                <label className="setting-label">背景类型</label>
                <div className="radio-group">
                  {[
                    { value: 'environment', label: '环境' },
                    { value: 'color', label: '颜色' },
                    { value: 'transparent', label: '透明' }
                  ].map(option => (
                    <label key={option.value} className="radio-option">
                      <input
                        type="radio"
                        name="backgroundType"
                        value={option.value}
                        checked={settings.backgroundType === option.value}
                        onChange={(e) => handleSettingChange('backgroundType', e.target.value)}
                      />
                      <span>{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {settings.backgroundType === 'color' && (
                <div className="setting-group">
                  <label className="setting-label">背景颜色</label>
                  <input
                    type="color"
                    value={settings.backgroundColor}
                    onChange={(e) => handleSettingChange('backgroundColor', e.target.value)}
                    className="color-picker"
                  />
                </div>
              )}
            </div>
          )}

          {activeTab === 'rendering' && (
            <div className="settings-section">
              <div className="setting-group">
                <label className="setting-label">色调映射</label>
                <select
                  value={settings.toneMapping}
                  onChange={(e) => handleSettingChange('toneMapping', e.target.value)}
                  className="setting-select"
                >
                  {toneMappingOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="setting-group">
                <label className="setting-label">曝光</label>
                <Slider
                  value={settings.exposure}
                  min={0.1}
                  max={3}
                  step={0.1}
                  onChange={(value) => handleSettingChange('exposure', value)}
                  showValue
                />
              </div>

              <div className="setting-group">
                <label className="setting-label">抗锯齿</label>
                <div className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={settings.antialias}
                    onChange={(e) => handleSettingChange('antialias', e.target.checked)}
                  />
                  <span className="toggle-slider"></span>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'effects' && (
            <div className="settings-section">
              <div className="setting-group">
                <label className="setting-label">
                  <Contrast size={14} />
                  对比度
                </label>
                <Slider
                  value={settings.contrast}
                  min={0.5}
                  max={2}
                  step={0.1}
                  onChange={(value) => handleSettingChange('contrast', value)}
                  showValue
                />
              </div>

              <div className="setting-group">
                <label className="setting-label">饱和度</label>
                <Slider
                  value={settings.saturation}
                  min={0}
                  max={2}
                  step={0.1}
                  onChange={(value) => handleSettingChange('saturation', value)}
                  showValue
                />
              </div>

              <div className="setting-group">
                <label className="setting-label">自动旋转</label>
                <div className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={settings.autoRotate}
                    onChange={(e) => handleSettingChange('autoRotate', e.target.checked)}
                  />
                  <span className="toggle-slider"></span>
                </div>
              </div>

              {settings.autoRotate && (
                <div className="setting-group">
                  <label className="setting-label">旋转速度</label>
                  <Slider
                    value={settings.rotationSpeed}
                    min={0.1}
                    max={3}
                    step={0.1}
                    onChange={(value) => handleSettingChange('rotationSpeed', value)}
                    showValue
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SceneSettings;
