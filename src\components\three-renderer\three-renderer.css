/* 3D渲染器基础样式 */
.three-renderer {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--color-primary-background);
  border-radius: var(--radius-l);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Canvas容器 */
.three-renderer__canvas {
  width: 100% !important;
  height: 100% !important;
  flex: 1;
  background: transparent !important;
}

/* 加载状态 */
.three-renderer__loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(--color-primary-background);
  z-index: 10;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-brand);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.three-renderer__loading span {
  color: var(--color-content-regular);
  font-size: var(--font-size);
  font-weight: 500;
}

/* 渲染信息面板已移除，使用性能监控组件替代 */

/* 响应式设计 */
@media (max-width: 768px) {
  .three-renderer__info {
    top: 12px;
    left: 12px;
    gap: 12px;
    padding: 6px 10px;
  }
  
  .info-label {
    font-size: 9px;
  }
  
  .info-value {
    font-size: 11px;
  }
}

/* 性能优化 */
.three-renderer__canvas canvas {
  image-rendering: optimizeSpeed;
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
  -ms-interpolation-mode: nearest-neighbor;
}

/* 禁用选择和拖拽 */
.three-renderer {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.three-renderer__canvas {
  outline: none;
}

/* 全屏模式支持 */
.three-renderer.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  border-radius: 0;
}

/* 错误状态 */
.three-renderer__error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(--color-primary-background);
  z-index: 10;
  gap: 16px;
}

.three-renderer__error-icon {
  width: 48px;
  height: 48px;
  color: var(--color-content-mute);
}

.three-renderer__error-message {
  color: var(--color-content-regular);
  font-size: var(--font-size);
  text-align: center;
  max-width: 300px;
}

/* 控制按钮样式增强 */
.three-renderer .control-button {
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.three-renderer .control-button:hover {
  background: var(--color-support-hover);
  transform: translateY(-1px);
}

/* 加载动画增强 */
.three-renderer__loading {
  background: linear-gradient(135deg, 
    var(--color-primary-background) 0%, 
    rgba(34, 105, 236, 0.05) 100%);
}

.loading-spinner {
  box-shadow: 0 0 20px rgba(34, 105, 236, 0.3);
}

/* 信息面板动画 */
.three-renderer__info {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题适配 */
.theme-dark .three-renderer {
  background: var(--color-primary-background);
}

.theme-dark .three-renderer__info {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--color-border);
}

/* 浅色主题适配（如果需要） */
.theme-light .three-renderer {
  background: #f5f5f5;
}

.theme-light .three-renderer__info {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-light .info-label {
  color: rgba(0, 0, 0, 0.6);
}

.theme-light .info-value {
  color: rgba(0, 0, 0, 0.9);
}
