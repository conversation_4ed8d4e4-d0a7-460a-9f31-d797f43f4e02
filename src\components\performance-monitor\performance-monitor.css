/* 性能监控组件样式 */
.performance-monitor {
  position: absolute;
  top: 16px;
  left: 16px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-m);
  border: 1px solid var(--color-border);
  padding: 12px;
  min-width: 120px;
  z-index: 10;
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
}

.performance-monitor:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.performance-monitor.expanded {
  min-width: 200px;
  cursor: default;
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.performance-main {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.fps-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.fps-value {
  font-size: 20px;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  line-height: 1;
}

.fps-label {
  font-size: 10px;
  color: var(--color-content-mute);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.quick-stats {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-item {
  font-size: 10px;
  color: var(--color-content-regular);
  font-family: 'Courier New', monospace;
}

.expand-indicator {
  font-size: 10px;
  color: var(--color-content-mute);
  transition: transform 0.2s ease;
}

.performance-monitor.expanded .expand-indicator {
  transform: rotate(0deg);
}

.performance-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--color-border);
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 11px;
  color: var(--color-content-mute);
}

.stat-value {
  font-size: 11px;
  color: var(--color-content-accent);
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* 性能图表 */
.performance-chart {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid var(--color-border);
}

.chart-label {
  font-size: 10px;
  color: var(--color-content-mute);
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 1px;
  height: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
  padding: 2px;
}

.chart-bar {
  flex: 1;
  min-height: 2px;
  border-radius: 1px;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.chart-bar:hover {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-monitor {
    top: 12px;
    left: 12px;
    padding: 10px;
    min-width: 100px;
  }
  
  .performance-monitor.expanded {
    min-width: 180px;
  }
  
  .fps-value {
    font-size: 18px;
  }
  
  .fps-label {
    font-size: 9px;
  }
  
  .stat-item {
    font-size: 9px;
  }
  
  .stat-label,
  .stat-value {
    font-size: 10px;
  }
}

/* 深色主题适配 */
.theme-dark .performance-monitor {
  background: rgba(0, 0, 0, 0.9);
  border-color: var(--color-border);
}

.theme-dark .performance-monitor:hover {
  background: rgba(0, 0, 0, 0.95);
}

/* 浅色主题适配 */
.theme-light .performance-monitor {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.9);
}

.theme-light .performance-monitor:hover {
  background: rgba(255, 255, 255, 0.95);
}

.theme-light .fps-label {
  color: rgba(0, 0, 0, 0.5);
}

.theme-light .stat-item {
  color: rgba(0, 0, 0, 0.7);
}

.theme-light .stat-label {
  color: rgba(0, 0, 0, 0.5);
}

.theme-light .stat-value {
  color: rgba(0, 0, 0, 0.9);
}

.theme-light .expand-indicator {
  color: rgba(0, 0, 0, 0.5);
}

.theme-light .chart-label {
  color: rgba(0, 0, 0, 0.5);
}

.theme-light .chart-bars {
  background: rgba(0, 0, 0, 0.05);
}

/* 性能警告状态 */
.performance-monitor.warning {
  border-color: #FF9800;
  box-shadow: 0 0 10px rgba(255, 152, 0, 0.3);
}

.performance-monitor.critical {
  border-color: #F44336;
  box-shadow: 0 0 10px rgba(244, 67, 54, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(244, 67, 54, 0.6);
  }
}

/* 加载状态 */
.performance-monitor.loading {
  opacity: 0.7;
}

.performance-monitor.loading .fps-value {
  animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 工具提示 */
.performance-monitor::before {
  content: '点击展开详细信息';
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.performance-monitor:hover::before {
  opacity: 1;
}

.performance-monitor.expanded::before {
  display: none;
}
