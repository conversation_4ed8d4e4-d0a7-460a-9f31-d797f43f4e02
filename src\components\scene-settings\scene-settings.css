/* 场景设置组件样式 */
.scene-settings {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.scene-settings__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.scene-settings__panel {
  position: relative;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  background: var(--color-dialog-background);
  border-radius: var(--radius-l);
  border: 1px solid var(--color-border);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-primary-background);
}

.settings-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-content-accent);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.settings-tabs {
  display: flex;
  background: var(--color-input-background);
  border-bottom: 1px solid var(--color-border);
}

.tab-button {
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: var(--color-content-regular);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  position: relative;
}

.tab-button:hover {
  background: var(--color-support);
  color: var(--color-content-accent);
}

.tab-button.active {
  background: var(--color-primary-background);
  color: var(--color-brand);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-brand);
}

.settings-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.settings-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--color-content-regular);
  display: flex;
  align-items: center;
  gap: 6px;
}

.setting-select {
  padding: 8px 12px;
  background: var(--color-input-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
  color: var(--color-content-accent);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.setting-select:hover {
  border-color: var(--color-brand);
}

.setting-select:focus {
  outline: none;
  border-color: var(--color-brand);
  box-shadow: 0 0 0 2px rgba(34, 105, 236, 0.2);
}

.radio-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 13px;
  color: var(--color-content-regular);
}

.radio-option input[type="radio"] {
  margin: 0;
  accent-color: var(--color-brand);
}

.color-picker {
  width: 60px;
  height: 32px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-picker:hover {
  border-color: var(--color-brand);
}

/* 切换开关样式 */
.toggle-switch {
  position: relative;
  width: 44px;
  height: 24px;
}

.toggle-switch input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-input-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background: var(--color-content-mute);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.toggle-switch input:checked + .toggle-slider {
  background: var(--color-brand);
  border-color: var(--color-brand);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(20px);
  background: white;
}

/* 滚动条样式 */
.settings-content::-webkit-scrollbar {
  width: 6px;
}

.settings-content::-webkit-scrollbar-track {
  background: var(--color-input-background);
  border-radius: 3px;
}

.settings-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.settings-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-content-mute);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scene-settings__panel {
    width: 95%;
    max-height: 90vh;
  }
  
  .settings-header {
    padding: 16px 20px;
  }
  
  .settings-header h3 {
    font-size: 16px;
  }
  
  .tab-button {
    padding: 10px 12px;
    font-size: 12px;
  }
  
  .settings-content {
    padding: 20px;
  }
  
  .setting-label {
    font-size: 12px;
  }
  
  .setting-select {
    font-size: 12px;
  }
}

/* 深色主题适配 */
.theme-dark .scene-settings__panel {
  background: var(--color-dialog-background);
  border-color: var(--color-border);
}

/* 浅色主题适配 */
.theme-light .scene-settings__panel {
  background: #ffffff;
  border-color: rgba(0, 0, 0, 0.1);
}

.theme-light .settings-header {
  background: #f8f9fa;
}

.theme-light .settings-tabs {
  background: rgba(0, 0, 0, 0.05);
}

.theme-light .tab-button {
  color: rgba(0, 0, 0, 0.7);
}

.theme-light .tab-button:hover {
  background: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.9);
}

.theme-light .tab-button.active {
  background: #ffffff;
  color: var(--color-brand);
}

.theme-light .setting-label {
  color: rgba(0, 0, 0, 0.8);
}

.theme-light .setting-select {
  background: #ffffff;
  border-color: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.9);
}

.theme-light .radio-option {
  color: rgba(0, 0, 0, 0.7);
}

.theme-light .toggle-slider {
  background: rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0.2);
}

.theme-light .toggle-slider:before {
  background: rgba(0, 0, 0, 0.5);
}
