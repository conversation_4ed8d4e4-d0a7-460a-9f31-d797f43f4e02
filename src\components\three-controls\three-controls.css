/* 3D控制面板样式 */
.three-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 10;
}

.controls-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-m);
  padding: 8px;
  border: 1px solid var(--color-border);
}

.controls-group:last-child {
  margin-bottom: 0;
}

/* 相机预设菜单 */
.camera-group {
  position: relative;
}

.camera-menu {
  position: absolute;
  top: 0;
  right: 100%;
  margin-right: 8px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(15px);
  border-radius: var(--radius-m);
  border: 1px solid var(--color-border);
  padding: 8px;
  min-width: 120px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideInRight 0.2s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.camera-preset-button {
  width: 100%;
  padding: 8px 12px;
  background: transparent;
  border: none;
  color: var(--color-content-regular);
  font-size: 12px;
  font-weight: 500;
  text-align: left;
  border-radius: var(--radius-s);
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.camera-preset-button:hover {
  background: var(--color-support);
  color: var(--color-content-accent);
}

.camera-preset-button:active {
  background: var(--color-brand);
  color: white;
}

/* 激活状态的按钮 */
.three-controls .icon-button.active {
  background: var(--color-brand);
  color: white;
}

.three-controls .icon-button.active:hover {
  background: var(--color-brand-hover);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .three-controls {
    top: 12px;
    right: 12px;
    gap: 6px;
  }
  
  .controls-group {
    padding: 6px;
    gap: 3px;
  }
  
  .camera-menu {
    right: 100%;
    margin-right: 6px;
    min-width: 100px;
  }
  
  .camera-preset-button {
    padding: 6px 10px;
    font-size: 11px;
  }
}

/* 工具提示增强 */
.three-controls .icon-button {
  position: relative;
}

.three-controls .icon-button::after {
  content: attr(title);
  position: absolute;
  top: 50%;
  right: 100%;
  margin-right: 8px;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: var(--radius-s);
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.three-controls .icon-button:hover::after {
  opacity: 1;
}

/* 深色主题适配 */
.theme-dark .controls-group {
  background: rgba(0, 0, 0, 0.8);
  border-color: var(--color-border);
}

.theme-dark .camera-menu {
  background: rgba(0, 0, 0, 0.95);
  border-color: var(--color-border);
}

/* 浅色主题适配 */
.theme-light .controls-group {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 0, 0, 0.1);
}

.theme-light .camera-menu {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(0, 0, 0, 0.1);
}

.theme-light .camera-preset-button {
  color: rgba(0, 0, 0, 0.7);
}

.theme-light .camera-preset-button:hover {
  background: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.9);
}

/* 动画效果 */
.three-controls {
  animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.controls-group {
  transition: all 0.2s ease;
}

.controls-group:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translateX(-2px);
}

/* 禁用状态 */
.three-controls .icon-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.three-controls .icon-button:disabled:hover {
  background: transparent;
  transform: none;
}

/* 特殊效果 */
.controls-group {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.camera-menu {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}
