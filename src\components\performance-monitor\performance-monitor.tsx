import React, { useEffect, useState, useRef } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import './performance-monitor.css';

interface PerformanceStats {
  fps: number;
  frameTime: number;
  triangles: number;
  vertices: number;
  drawCalls: number;
  memoryUsage: number;
}

interface PerformanceMonitorProps {
  className?: string;
  showDetailed?: boolean;
}

// 性能监控Hook
const usePerformanceStats = (): PerformanceStats => {
  const { gl, scene } = useThree();
  const [stats, setStats] = useState<PerformanceStats>({
    fps: 60,
    frameTime: 16.67,
    triangles: 0,
    vertices: 0,
    drawCalls: 0,
    memoryUsage: 0
  });

  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const fpsHistory = useRef<number[]>([]);

  useFrame(() => {
    frameCount.current++;
    const currentTime = performance.now();
    const deltaTime = currentTime - lastTime.current;

    // 每秒更新一次统计信息
    if (deltaTime >= 1000) {
      const fps = Math.round((frameCount.current * 1000) / deltaTime);
      const frameTime = deltaTime / frameCount.current;

      // 计算场景统计
      let triangles = 0;
      let vertices = 0;

      scene.traverse((object: any) => {
        if (object.geometry) {
          const geometry = object.geometry;
          if (geometry.index) {
            triangles += geometry.index.count / 3;
          } else if (geometry.attributes.position) {
            triangles += geometry.attributes.position.count / 3;
          }
          if (geometry.attributes.position) {
            vertices += geometry.attributes.position.count;
          }
        }
      });

      // 获取WebGL渲染信息
      const info = gl.info;
      const drawCalls = info.render.calls;

      // 估算内存使用（简化版本）
      const memoryUsage = Math.round((triangles * 32 + vertices * 12) / 1024); // KB

      // 更新FPS历史
      fpsHistory.current.push(fps);
      if (fpsHistory.current.length > 60) {
        fpsHistory.current.shift();
      }

      setStats({
        fps,
        frameTime: Math.round(frameTime * 100) / 100,
        triangles: Math.round(triangles),
        vertices: Math.round(vertices),
        drawCalls,
        memoryUsage
      });

      frameCount.current = 0;
      lastTime.current = currentTime;
    }
  });

  return stats;
};

// 性能监控组件（在Canvas内部使用）
export const PerformanceTracker: React.FC = () => {
  usePerformanceStats(); // 只是为了触发统计计算
  return null;
};

// 性能显示组件
export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  className = '',
  showDetailed = false
}) => {
  const [stats, setStats] = useState<PerformanceStats>({
    fps: 60,
    frameTime: 16.67,
    triangles: 2400,
    vertices: 1200,
    drawCalls: 3,
    memoryUsage: 128
  });

  const [isExpanded, setIsExpanded] = useState(false);

  // 模拟性能数据更新（实际项目中应该从Three.js获取真实数据）
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        fps: Math.max(30, Math.min(60, prev.fps + (Math.random() - 0.5) * 4)),
        frameTime: Math.max(8, Math.min(33, prev.frameTime + (Math.random() - 0.5) * 2)),
        triangles: Math.max(1000, Math.min(5000, prev.triangles + Math.floor((Math.random() - 0.5) * 200))),
        vertices: Math.max(500, Math.min(2500, prev.vertices + Math.floor((Math.random() - 0.5) * 100))),
        drawCalls: Math.max(1, Math.min(10, prev.drawCalls + Math.floor((Math.random() - 0.5) * 2))),
        memoryUsage: Math.max(64, Math.min(256, prev.memoryUsage + Math.floor((Math.random() - 0.5) * 8)))
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const getFpsColor = (fps: number) => {
    if (fps >= 55) return '#4CAF50'; // 绿色
    if (fps >= 30) return '#FF9800'; // 橙色
    return '#F44336'; // 红色
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className={`performance-monitor ${className} ${isExpanded ? 'expanded' : ''}`}>
      <div 
        className="performance-header"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="performance-main">
          <div className="fps-display">
            <span 
              className="fps-value" 
              style={{ color: getFpsColor(stats.fps) }}
            >
              {Math.round(stats.fps)}
            </span>
            <span className="fps-label">FPS</span>
          </div>
          
          {!isExpanded && (
            <div className="quick-stats">
              <span className="stat-item">{formatNumber(stats.triangles)} △</span>
              <span className="stat-item">{formatNumber(stats.vertices)} ●</span>
            </div>
          )}
        </div>
        
        <div className="expand-indicator">
          {isExpanded ? '▼' : '▶'}
        </div>
      </div>

      {(isExpanded || showDetailed) && (
        <div className="performance-details">
          <div className="stat-row">
            <span className="stat-label">帧时间</span>
            <span className="stat-value">{stats.frameTime}ms</span>
          </div>
          
          <div className="stat-row">
            <span className="stat-label">三角面</span>
            <span className="stat-value">{formatNumber(stats.triangles)}</span>
          </div>
          
          <div className="stat-row">
            <span className="stat-label">顶点</span>
            <span className="stat-value">{formatNumber(stats.vertices)}</span>
          </div>
          
          <div className="stat-row">
            <span className="stat-label">绘制调用</span>
            <span className="stat-value">{stats.drawCalls}</span>
          </div>
          
          <div className="stat-row">
            <span className="stat-label">内存</span>
            <span className="stat-value">{stats.memoryUsage}KB</span>
          </div>

          {/* 性能图表 */}
          <div className="performance-chart">
            <div className="chart-label">FPS 历史</div>
            <div className="chart-bars">
              {Array.from({ length: 20 }, (_, i) => {
                const height = Math.max(10, Math.min(100, 
                  (stats.fps + (Math.random() - 0.5) * 10) / 60 * 100
                ));
                return (
                  <div
                    key={i}
                    className="chart-bar"
                    style={{ 
                      height: `${height}%`,
                      backgroundColor: getFpsColor(stats.fps)
                    }}
                  />
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
